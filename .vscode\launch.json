{"version": "0.2.0", "configurations": [{"name": "archiving_job", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["archiving_job"]}, {"name": "core_api", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["core_api"]}, {"name": "enrichment_client", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["enrichment_client"]}, {"name": "fwd_scom_optic", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["fwd_scom_optic"]}, {"name": "heartbeat_job", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["heartbeat_job"]}, {"name": "icinga_events", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["icinga_events"]}, {"name": "icinga_objects", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["icinga_objects"]}, {"name": "ing_zabbix", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["ing_zabbix"]}, {"name": "mon_adva", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_adva"]}, {"name": "mon_big_data", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_big_data"]}, {"name": "mon_ca_spectrum", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_ca_spectrum"]}, {"name": "mon_certificates", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_certificates"]}, {"name": "mon_cnms", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_cnms"]}, {"name": "mon_datalines", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_datalines"]}, {"name": "mon_dica", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_dica"]}, {"name": "mon_gsx", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_gsx"]}, {"name": "mon_local6", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_local6"]}, {"name": "mon_lucent_oms", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_lucent_oms"]}, {"name": "mon_netact", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_netact"]}, {"name": "mon_occ", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_occ"]}, {"name": "mon_openshift", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_openshift"]}, {"name": "mon_pem", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_pem"]}, {"name": "mon_sap_solman", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_sap_solman"]}, {"name": "mon_scada", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_scada"]}, {"name": "mon_scom", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_scom"]}, {"name": "mon_stonebranch", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_stonebranch"]}, {"name": "mon_struxureware", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_struxureware"]}, {"name": "mon_vrealize", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_vrealize"]}, {"name": "mon_websphere_mq", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_websphere_mq"]}, {"name": "mon_zabbix", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["mon_zabbix"]}, {"name": "optic_matcher", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["optic_matcher"]}, {"name": "sap_air_client", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["sap_air_client"]}, {"name": "sbom_tools", "type": "debugpy", "request": "launch", "module": "a2110_olympus.run", "args": ["sbom_tools"]}, {"name": "Python: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal"}, {"name": "Python: Debug Test", "type": "debugpy", "request": "launch", "purpose": ["debug-test"], "justMyCode": false}, {"name": "Python: Debug E2E Tests", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["--run-e2e"], "justMyCode": false}]}