"""Utils module for mon-datalines."""

import datetime
import json
import socket

import pandas as pd

from mon_datalines.lookup_tables import datalines_alarm_list
from olympus_common import enums, utils
from olympus_common.elastic_apm import CaptureSpan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def ci_id(host: str) -> str:
    """Indicate the CI ID."""
    try:
        response = socket.gethostbyaddr(str(host))
        host_name = response[0].split(".")[0]
        return host_name
    except socket.herror:
        return host


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_type() -> str:
    """Indicate the way the clear is made, possible values between "Not Defined":0, "Manually":1, "Automatically":2."""
    return enums.ClearType.AUTOMATICALLY.value


def agent() -> str:
    """Name of the Agent."""
    return "DataLines"


def alert_group(tls_trap_description: str) -> str:
    """Indicate the alert group."""
    tls_trap_description = str(tls_trap_description)
    return tls_trap_description.split(".")[0]


def alert_key(tls_trap_description: str, if_index: int) -> str:
    """Indicate the alert key.

    We use the metric_name as first part to ensure "on" and "off" alerts are grouped together.
    """
    return f"{metric_name(tls_trap_description)} ifindex={if_index}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def identifier(tls_description: str, if_index: int, host: str) -> str:
    """Indicate the identifier."""
    return f"{agent()}/{node(host)}/{alert_group(tls_description)}/{alert_key(tls_description, if_index)}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def manager() -> str:
    """Name of the Module."""
    return "mon-datalines"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def node(host: str | None) -> str:
    """Indicate the Hostname or the IP value of the Hostname e.g:'bdiraiorlc012'."""
    return host or "N/A"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def node_alias(host: str | None) -> str:
    """Indicate the Hostname or the IP value of the Hostname."""
    return host or "N/A"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def raise_time(timestamp: str) -> datetime.datetime:
    """First date and time when the problem alarm was generated (timestamp sent by EMS)."""
    timestamp_str_ = timestamp.split(".")[0]
    time = datetime.datetime.strptime(timestamp_str_, "%Y-%m-%dT%H:%M:%S")
    return time


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_time(event_type_: str, raise_time_: datetime.datetime) -> datetime.datetime | None:
    """Indicate the clear time of the alarm."""
    return raise_time_ if event_type_ == "clear" else None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def scope() -> str:
    """Represent the MD group that is responsible for  handling that alarm."""
    return enums.Scope.TE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def metric_type() -> str:
    """Indicate the alarm metrics."""
    return ""


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def metric_name(tls_trap_description: str) -> str:
    """Indicate the monitored element name of the alarm."""
    tls_trap_description = str(tls_trap_description)
    return f"{tls_trap_description.split('.')[-1].split(' ')[0]}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def top_level() -> str:
    """Indicate the alarm top level."""
    return "A1298"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def summary(tls_trap_description: str, ci_type: str = "") -> str:
    """Represent the alarm summary."""
    if ci_type != "":
        ci_type = f"- {ci_type}"
    return f"{tls_trap_description} {ci_type}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def severity(tls_trap_severity_level: str, event_type_: str) -> int:
    """Indicate the alarm severity."""
    if event_type_ == "clear":
        return enums.Severity.INDETERMINATE.value

    match tls_trap_severity_level:
        case "0":
            return enums.Severity.INDETERMINATE.value
        case "1":
            return enums.Severity.WARNING.value
        case "2":
            return enums.Severity.MINOR.value
        case "3":
            return enums.Severity.MAJOR.value
        case _:
            return enums.Severity.CRITICAL.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def event_type(tls_trap_description: str) -> str:
    """Indicate if the alarm is a clear or not."""
    return (
        enums.AlarmType.RESOLUTION.value
        if str(tls_trap_description).split(" ")[-1] == "off"
        else enums.AlarmType.PROBLEM.value
    )


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def additional_data(tls_trap_description: str) -> str:
    """Indicate in which platform the server runs. This data is extracted from the event."""
    responsible = datalines_alarm_list.get_responsible(str(tls_trap_description).split(" ")[0])
    return json.dumps({"responsible_department": f"{responsible}"})


def event_id() -> str:
    """Indicate the event ID."""
    return ""


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_level(event_type_: str) -> str:
    """Indicate if the alarm is a clear or not."""
    return enums.AlarmType.PROBLEM.value if event_type_ == "clear" else enums.AlarmType.RESOLUTION.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def actionable(row: pd.Series) -> bool:
    """Indicate if an alarm is actionable or not."""
    tls_trap_description = row["tlsTrapDescription"]
    severity = row["severity"]
    datalines_actionable = datalines_alarm_list.is_actionable_alarm(str(tls_trap_description).split(" ")[0])
    if datalines_actionable == 1 and severity == enums.Severity.CRITICAL.value:
        return True
    else:
        return False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def handle_time() -> datetime.datetime:
    """Determine the handle time, i.e. the time when the alarm is handled by Olympus."""
    return utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def wake_up_time(raise_time: datetime.datetime) -> datetime.datetime:
    """Return the wake-up time."""
    return raise_time + datetime.timedelta(0, delay())


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def delay() -> int:
    """Return the delay."""
    return 120
