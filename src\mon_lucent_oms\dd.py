"""Details design implementation for mon-lucent-oms."""

from datetime import datetime, timedelta

import pandas as pd
from sqlalchemy.orm import Session

from mon_lucent_oms import statics
from olympus_common import db, enums
from olympus_common import pd as olympus_pd
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _agent() -> str:
    """Return the agent name for the DD."""
    return "LucentOMS"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def actionable_alarm(row: pd.Series) -> bool:
    """Return the actionable alarm value."""
    et = event_type(row)
    sv = severity(row)
    ak = _alert_key(row)
    ad = additional_data(row)

    # Rule (No) for:
    # - resolution alarms
    # - problem alarms with severity 1 (Indeterminate)
    # - problem alarms with AlertKey like "VC.*UNEQ.*"
    if (
        et == enums.AlarmType.RESOLUTION.value
        or (et == enums.AlarmType.PROBLEM.value and sv == enums.Severity.INDETERMINATE.value)
        or (et == enums.AlarmType.PROBLEM.value and "VC.*UNEQ.*" in ak)
    ):
        return False

    # Rule (Yes) for Problem Alarms
    # For problem alarms having either @AdditionalInformation in specific list
    # or @AdditionalInformation contains "DEG"
    if et == enums.AlarmType.PROBLEM.value and (
        ad
        in [
            "HouseKeeping",
            "POWAcFLR",
            "POWBcFLR",
            "STM1cLOS",
            "STM4cLOS",
            "STM16cLOS",
            "TBACKcUPM",
            "TLINKcFLR",
            "TLINKcUNEQ",
            "TSCOcSQ",
        ]
        or ad in "DEG"
    ):
        return True

    # if severity=5 (critical) THEN ActionableAlarm=1 (Yes)
    if sv == enums.Severity.CRITICAL.value:
        return True

    # Rule (No) for all other problem alarms
    return False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def additional_data(row: pd.Series) -> str:
    """Return the additional data."""
    alarm_name: str = row["alarm_name"]
    return alarm_name


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_type() -> str:
    """Return the clear_type."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def ci_id(row: pd.Series) -> str:
    """Return the CI ID value."""
    name_network_resource: str = row["name_network_resource"]
    return name_network_resource


def _alert_key(row: pd.Series) -> str:
    """Return the alert key."""
    n: str = row["alarm_name"]
    pa: str = row["physical_port_address"]
    # clf is always empty so we don't need it
    return f"{n} {pa}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def manager() -> str:
    """Return the manager."""
    return "mon-lucent-oms"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def node(row: pd.Series) -> str:
    """Return the node."""
    cla: str = row["name_network_resource"]
    n: str = row["alarm_name"]
    if not n:
        return "Unknown"
    return cla


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def scope() -> str:
    """Return the scope."""
    return enums.Scope.TE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def severity(row: pd.Series) -> int | None:
    """Return the severity."""
    sv: int = int(row["alarm_severity"])
    match sv:
        case 1:
            return enums.Severity.CRITICAL.value
        case 2:
            return enums.Severity.MAJOR.value
        case 3:
            return enums.Severity.MINOR.value
        case 4:
            return enums.Severity.WARNING.value
        case 5:
            return enums.Severity.INDETERMINATE.value
    return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def summary(row: pd.Series) -> str:
    """Return the summary of the event."""
    summary = ""
    n: str = row["alarm_name"]
    txt: str = row["alarm_text_string"]
    if not n:
        summary = "No name or description"
    else:
        if not txt:
            summary = n
        else:
            summary = txt

    return summary


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def top_level() -> str:
    """Return the top level."""
    return "A1029"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def event_type(row: pd.Series) -> str:
    """Return the alarm type."""
    alarm_state: str = row["alarm_state"]
    if alarm_state == "A":
        return enums.AlarmType.PROBLEM.value
    else:
        return enums.AlarmType.RESOLUTION.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def handle_time() -> datetime:
    """Return the handle time."""
    return olympus_utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_time(row: pd.Series) -> datetime | None:
    """Return the clear time."""
    if event_type(row) == enums.AlarmType.RESOLUTION.value:
        return raise_time(row)
    return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def wake_up_time(row: pd.Series) -> datetime:
    """Return the wake up time."""
    return raise_time(row) + timedelta(0, delay())


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def delay() -> int:
    """Return the delay for lucent oms events snooze mechanism.."""
    return 120


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def raise_time(row: pd.Series) -> datetime:
    """Return the raise time."""
    raise_time: float = row["raise_time_raw"]  # epoch time
    return datetime.fromtimestamp(raise_time)


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the LucentOMS detailed design."""
    agent_id = db.Agent.get_agent_id_from_name(_agent(), session)
    return transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    # Add the columns that are not currently present in big data to avoid errors.
    for column in statics.COLUMNS_TO_KEEP:
        df = olympus_pd.check_field_in_df(df, column, "")
    df = olympus_pd.clean_raw_dataframe(df, statics.COLUMNS_RENAMING)

    # hardcoded values
    df["agent_id"] = agent_id
    df["manager"] = manager()
    df["action_class"] = scope()
    df["handle_time"] = handle_time()
    df["clear_type"] = clear_type()
    df["top_level"] = top_level()

    # computed values
    df["severity"] = df.apply(severity, axis=1)
    df["additional_data"] = df.apply(additional_data, axis=1)
    df["node"] = df.apply(node, axis=1)
    df["node_alias"] = df["node"]
    df["ci_id"] = df.apply(ci_id, axis=1)
    df["raise_time"] = df.apply(raise_time, axis=1)
    df["clear_time"] = df.apply(clear_time, axis=1)
    df["wake_up_time"] = df.apply(wake_up_time, axis=1)
    df["summary"] = df.apply(summary, axis=1)
    df["actionable"] = df.apply(actionable_alarm, axis=1)
    df["event_type"] = df.apply(event_type, axis=1)

    return df
