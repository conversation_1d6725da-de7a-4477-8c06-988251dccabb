"""Static module for mon-lucent-oms."""

COLUMNS_RENAMING: dict[str, str] = {
    "SNMPv2-SMI::enterprises.1751.1.102.1": "alarm_qualifier",  # q
    "SNMPv2-SMI::enterprises.1751.1.102.2": "raise_time_raw",  # tm
    "SNMPv2-SMI::enterprises.1751.1.102.3": "alarm_severity",  # sv
    "SNMPv2-SMI::enterprises.1751.1.102.4": "alarm_name",  # n
    "SNMPv2-SMI::enterprises.1751.1.102.5": "service_affecting_flag",  # af
    "SNMPv2-SMI::enterprises.1751.1.102.6": "name_network_resource",  # cla
    "SNMPv2-SMI::enterprises.1751.1.102.7": "physical_port_address",  # pa
    "SNMPv2-SMI::enterprises.1751.1.102.8": "additional_info",  # ai
    "SNMPv2-SMI::enterprises.1751.1.102.9": "alarm_type",  # cat
    "SNMPv2-SMI::enterprises.1751.1.102.10": "alarm_text_string",  # txt
    "SNMPv2-SMI::enterprises.1751.1.102.11": "alarm_state",  # s
    "SNMPv2-SMI::enterprises.1751.1.102.12": "sequence_number_tag",  # t
}

COLUMNS_TO_KEEP = list(COLUMNS_RENAMING.keys())
