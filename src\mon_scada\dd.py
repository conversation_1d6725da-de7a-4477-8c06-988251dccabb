"""Details Design module for mon-scada."""

import pandas as pd
from sqlalchemy.orm import Session

from mon_scada import common_fields, csj, kst_bns_mst, statics
from mon_scada import enums as scada_enums
from mon_scada.config import config
from olympus_common import db, enums
from olympus_common import pd as olympus_pd
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the SCADA Details Design."""
    agent_name = common_fields.agent()
    agent_id = db.Agent.get_agent_id_from_name(agent_name, session)
    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    df = olympus_pd.clean_raw_dataframe(df=df, columns_renaming=statics.COLUMNS_RENAMING)

    # Hardcoded fields
    df["agent_id"] = agent_id
    df["manager"] = common_fields.manager()
    df["node"] = common_fields.node()
    df["node_alias"] = df["node"]
    df["handle_time"] = common_fields.handle_time()
    df["additional_data"] = common_fields.additional_data()
    df["action_class"] = common_fields.action_class()
    df["clear_type"] = common_fields.clear_type()
    df["metric_type"] = common_fields.metric_type()
    df["delay"] = common_fields.delay()

    # Common computed fields
    df["summary"] = df.apply(common_fields.summary, axis=1)
    df["wake_up_time"] = df.apply(common_fields.wake_up_time, axis=1)
    # df["event_type"] = df.apply(common.event_type, axis=1) the code has been removed on the passport.
    df["raise_time"] = df.apply(common_fields.raise_time, axis=1)

    # CSJ computed fields
    if config.scada_cc == scada_enums.ScadaCC.CSJ.value:
        df["ci_id"] = df.apply(csj.ci_id, axis=1)
        df["metric_name"] = df.apply(csj.metric_name, axis=1)
        df["severity"] = df.apply(csj.severity, axis=1)
        df["clear_time"] = df.apply(csj.clear_time, axis=1)
        df["top_level"] = csj.top_level()

    # KST_BNS_MST computed fields
    elif config.scada_cc in [
        scada_enums.ScadaCC.KST.value,
        scada_enums.ScadaCC.BNS.value,
        scada_enums.ScadaCC.MST.value,
    ]:
        df["ci_id"] = df.apply(kst_bns_mst.ci_id, axis=1)
        df["metric_name"] = df.apply(kst_bns_mst.metric_name, axis=1)
        df["severity"] = df.apply(kst_bns_mst.severity, axis=1)
        df["clear_time"] = df.apply(kst_bns_mst.clear_time, axis=1)
        df["top_level"] = kst_bns_mst.top_level()
    else:
        raise ValueError(f"scada_cc {config.scada_cc} does not exist.")

    return df
