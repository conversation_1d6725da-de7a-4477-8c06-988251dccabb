"""Tests for the `main` module."""

import json
from pathlib import Path

import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from tests.olympus_common.mocked_db import MockedDBModule
from tests.utils import remove_middleware


@pytest.mark.parametrize("input_file", ["01.json"])
def test_main(mocker: MockerFixture, input_file: str):
    """Test the webhook endpoint with test data."""
    from mon_big_data.main import app

    remove_middleware(app, "AuthMiddleware")

    mocked_database_writer = mocker.patch("mon_big_data.main.DatabaseWriter")
    mocker.patch("mon_big_data.dd.db", new=MockedDBModule())

    client = TestClient(app)
    url = "http://127.0.0.1:8000/webhook"
    path = Path(__file__).parent / "data" / input_file
    test_content = json.loads(path.read_text())
    testdata = test_content.get("data", [])

    for item in testdata:
        item_input = item["input"]
        webhook_payload = {"alerts": [item_input]}
        response = client.post(url, json=webhook_payload)
        assert response.status_code == 200
        assert mocked_database_writer.return_value.success.called
