{"data": [{"input": {"server": "iictyiaplv097", "date": "06/27/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-10T21:56:10.621541040Z", "repair_person": "", "severity": "CRITICAL", "mhandle": "0x878161e", "mname": "lpatriuf8", "alarm_id": "1242443", "event_message": "Thu 27 Jun, 2024 - 17:33:48 - The Client \"***********\" connected to Access Point \"lpatriuf8\" exceeded the critical threshold (90%) upload volume limit.\n\nClient Information:-\n------------------------\n    Mac Address: BC.F4.D4.2E.93.FF\n    IP Address: ***********\n    User Name: BELGI<PERSON>RAIL\\EXU071\n    Upload Volume Used : 10362 Megabytes\n    Maximum Upload Volume Configured Per Client : 10240 Megabytes\n\n(event [0x0676000b])\n", "mtype": "AccessPoint", "time": "17:33:48", "cause_code": "676000b", "clearable": "FALSE", "event_type": "1", "dtype": "IP Device", "ipaddress": "***********", "mthandle": "0x6760005", "ackd": "FALSE", "secstr": "", "event.uuid": "fb5354f2-3e00-4825-84cd-d3b40eff0080", "event.kafka.offset": 2114853, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-10T21:56:10.483Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Thu 27 Jun, 2024 - 17:33:48 - The Client \"***********\" connected to Access Point \"lpatriuf8\" exceeded the critical threshold (90%) upload volume limit.", "ci_id": "lpatriuf8", "node": "***********", "metric_name": "0676000b", "clear_time": null, "event_id": "1242443", "node_alias": "***********", "actionable": false, "additional_data": "{\"cause_code\": \"676000b\"}", "raise_time": "2024-06-27 15:33:48", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "06/28/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-10T23:56:10.311683517Z", "repair_person": "", "severity": "MINOR", "mhandle": "0x8717c33", "mname": "rsmolxx1v.network.railb.be", "alarm_id": "2231433", "event_message": "Fri 28 Jun, 2024 - 13:18:34 A(n) SwCiscoIOS device, named rsmolxx1v.network.railb.be, has detected a Communication Link Down. ifIndex = 250, ifAdminStatus = , ifOperStatus = , ifDescr = Vlan957, ifType = 53.\n\nAdditional Variables:\nsysObjectID = \nsysName = \nlocIfReason = down\nifReasonForStatusCes = \nifPhysLocationCes = \nifPhysRelPosCes = \nifIpAddrCes = \nifNameCes = \nifTunnelRemoteIpAddrCes = \nifName = \nifAlias = \nusdSnmpTrapTrapSeverity = \n\n(event [0x00220101])\nOnly displaying most recent of 2 event messages.\n", "mtype": "SwCiscoIOS", "time": "13:18:10", "cause_code": "220001", "clearable": "TRUE", "event_type": "1", "dtype": "Cat37xxStack", "ipaddress": "***********", "mthandle": "0x2100b2", "ackd": "FALSE", "secstr": "", "event.uuid": "4ca75dbb-b30c-420a-9c7a-4b1cc2396a0e", "event.kafka.offset": 2111980, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-10T23:56:10.275Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016"}, "output": {"severity": 3, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Link Up/Down", "ci_id": "rsmolxx1", "node": "***********", "metric_name": "00220001", "clear_time": null, "event_id": "2231433", "node_alias": "***********", "actionable": false, "additional_data": "{\"cause_code\": \"220001\"}", "raise_time": "2024-06-28 11:18:10", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "06/30/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-10T23:56:10.887890477Z", "repair_person": "", "severity": "MINOR", "mhandle": "0x8776533", "mname": "rsdiest1v.network.railb.be", "alarm_id": "4263152", "event_message": "Sun 30 Jun, 2024 - 07:16:01 A(n) SwCiscoIOS device, named rsdiest1v.network.railb.be, has detected a Communication Link Down. ifIndex = 86, ifAdminStatus = , ifOperStatus = , ifDescr = TenGigabitEthernet2/1/6, ifType = 6.\n\nAdditional Variables:\nsysObjectID = \nsysName = \nlocIfReason = down\nifReasonForStatusCes = \nifPhysLocationCes = \nifPhysRelPosCes = \nifIpAddrCes = \nifNameCes = \nifTunnelRemoteIpAddrCes = \nifName = \nifAlias = \nusdSnmpTrapTrapSeverity = \n\n(event [0x00220101])\n", "mtype": "SwCiscoIOS", "time": "07:16:01", "cause_code": "220001", "clearable": "TRUE", "event_type": "1", "dtype": "Cisco Catalyst 9300 Switch", "ipaddress": "*************", "mthandle": "0x2100b2", "ackd": "FALSE", "secstr": "", "event.uuid": "71e8bf13-9021-4e4a-978f-0aea7d8c4f47", "event.kafka.offset": 2111981, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-10T23:56:10.753Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016"}, "output": {"severity": 3, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Link Up/Down", "ci_id": "rsdiest1", "node": "*************", "metric_name": "00220001", "clear_time": null, "event_id": "4263152", "node_alias": "*************", "actionable": false, "additional_data": "{\"cause_code\": \"220001\"}", "raise_time": "2024-06-30 05:16:01", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv097", "date": "07/01/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-10T21:56:07.324456400Z", "repair_person": "", "severity": "CRITICAL", "mhandle": "0x8781a23", "mname": "lpatriu52", "alarm_id": "6007682", "event_message": "Mon 01 Jul, 2024 - 16:10:31 - The Client \"**********\" connected to Access Point \"lpatriu52\" exceeded the critical threshold (90%) download volume limit.\n\nClient Information:-\n------------------------\n    Mac Address: 38.FC.98.4B.33.45\n    IP Address: **********\n    User Name: B<PERSON><PERSON><PERSON>RAIL\\XTR0000\n    Downloaded Volume Used : 9776 Megabytes\n    Maximum Download Volume Configured Per Client : 10240 Megabytes\n\n(event [0x06760008])\n", "mtype": "AccessPoint", "time": "16:10:31", "cause_code": "6760008", "clearable": "FALSE", "event_type": "1", "dtype": "IP Device", "ipaddress": "************", "mthandle": "0x6760005", "ackd": "FALSE", "secstr": "", "event.uuid": "b041d5e5-a47b-43e2-83b0-d36704390eb2", "event.kafka.offset": 2114852, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-10T21:56:07.289Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Mon 01 Jul, 2024 - 16:10:31 - The Client \"**********\" connected to Access Point \"lpatriu52\" exceeded the critical threshold (90%) download volume limit.", "ci_id": "lpatriu52", "node": "************", "metric_name": "06760008", "clear_time": null, "event_id": "6007682", "node_alias": "************", "actionable": false, "additional_data": "{\"cause_code\": \"6760008\"}", "raise_time": "2024-07-01 14:10:31", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv097", "date": "07/02/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-10T21:56:18.983816467Z", "repair_person": "", "severity": "CRITICAL", "mhandle": "0x8781737", "mname": "lpatriuf2", "alarm_id": "7010139", "event_message": "Tue 02 Jul, 2024 - 11:58:27 - The Client \"***********\" connected to Access Point \"lpatriuf2\" exceeded the critical threshold (90%) upload volume limit.\n\nClient Information:-\n------------------------\n    Mac Address: BC.F4.D4.2E.A4.4F\n    IP Address: ***********\n    User Name: BELGIANRAIL\\ORD7200\n    Upload Volume Used : 30883 Megabytes\n    Maximum Upload Volume Configured Per Client : 10240 Megabytes\n\n(event [0x0676000b])\nOnly displaying most recent of 2 event messages.\n", "mtype": "AccessPoint", "time": "10:27:31", "cause_code": "676000b", "clearable": "FALSE", "event_type": "1", "dtype": "IP Device", "ipaddress": "***********", "mthandle": "0x6760005", "ackd": "FALSE", "secstr": "", "event.uuid": "66fc2e0e-1c6a-44ae-8c50-bd0c56e00bbf", "event.kafka.offset": 2111809, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-10T21:56:18.934Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Tue 02 Jul, 2024 - 11:58:27 - The Client \"***********\" connected to Access Point \"lpatriuf2\" exceeded the critical threshold (90%) upload volume limit.", "ci_id": "lpatriuf2", "node": "***********", "metric_name": "0676000b", "clear_time": null, "event_id": "7010139", "node_alias": "***********", "actionable": false, "additional_data": "{\"cause_code\": \"676000b\"}", "raise_time": "2024-07-02 08:27:31", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv097", "date": "07/03/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-10T21:56:22.618677146Z", "repair_person": "", "severity": "MINOR", "mhandle": "0x8718ef7", "mname": "lpantbe19", "alarm_id": "8632664", "event_message": "Wed 03 Jul, 2024 - 15:34:32 - The Client \"***********\" connected to Access Point \"lpantbe19\" exceeded the minor threshold (70%) upload volume limit.\n\nClient Information:-\n------------------------\n    Mac Address: 6A.14.CE.30.7F.82\n    IP Address: ***********\n    User Name: KJX7701\n    Upload Volume Used : 7314 Megabytes\n    Maximum Upload Volume Configured Per Client : 10240 Megabytes\n\n(event [0x0676000d])\n", "mtype": "AccessPoint", "time": "15:34:32", "cause_code": "676000d", "clearable": "FALSE", "event_type": "1", "dtype": "IP Device", "ipaddress": "*************", "mthandle": "0x6760005", "ackd": "FALSE", "secstr": "", "event.uuid": "e1d717b6-b177-4124-b6e5-bbb087029980", "event.kafka.offset": 2111811, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-10T21:56:22.582Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Wed 03 Jul, 2024 - 15:34:32 - The Client \"***********\" connected to Access Point \"lpantbe19\" exceeded the minor threshold (70%) upload volume limit.", "ci_id": "lpantbe19", "node": "*************", "metric_name": "0676000d", "clear_time": null, "event_id": "8632664", "node_alias": "*************", "actionable": false, "additional_data": "{\"cause_code\": \"676000d\"}", "raise_time": "2024-07-03 13:34:32", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/04/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-04T12:24:06.675107715Z", "repair_person": "", "severity": "MINOR", "mhandle": "0x87535dc", "mname": "iictyiaplv077.msnet.railb.be", "alarm_id": "9850262", "event_message": "Thu 04 Jul, 2024 - 14:24:05 - Unknown alert received from device iictyiaplv077.msnet.railb.be of type Pingable. Device Time 0+00:00:00. (Trap type *******.*******.867.6.1) (event [0x00010801])\nTrap var bind data: \nOID:  *******.*******.0  Value:  Thu Jul 04 12:24:05 UTC 2024\nOID:  *******.*******.4.1.0  Value:  *******.*******.867.1\nOID:  *******.********.3.0  Value:  *************\nOID:  *******.*******.867.2  Value:  36e39f4e-2e13-4b5a-abfe-698a67ecd4b8\nOID:  *******.*******.867.3  Value:  SYSTEM-MANAGED-SERVICES\nOID:  *******.*******.867.4  Value:  OK\nOID:  *******.*******.867.5  Value:   Managed Service elasticsearch is RUNNING \nOID:  *******.*******.867.6  Value:  []\nOID:  *******.*******.867.7  Value:  1.0.0\nOID:  *******.*******.867.8  Value:  Thu Jul 04 12:23:35 UTC 2024\nOID:  *******.*******.867.9  Value:  Cisco DNA Center System\nOID:  *******.*******.867.10  Value:  Platform Services\nOID:  *******.*******.867.11  Value:  INFO\nOID:  *******.*******.867.12  Value:  SYSTEM\nOID:  *******.*******.867.13  Value:  3\nOID:  *******.*******.867.14  Value:  DNAC API\nOID:  *******.*******.867.15  Value:  {\"Message\":\" Managed Service elasticsearch is RUNNING \",\"Instance\":\"dnacenter.msnet.railb.be : \",\"State\":\"OK\",\"Tags\":\"Managed Services\"}\n", "mtype": "Pingable", "time": "14:24:05", "cause_code": "10801", "clearable": "TRUE", "event_type": "1", "dtype": "IP Device", "ipaddress": "*************", "mthandle": "0x10290", "ackd": "FALSE", "secstr": "", "event.uuid": "7b050609-f204-4f9c-9290-d173b126475c", "event.kafka.offset": 2105617, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-04T12:24:06.539Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Thu 04 Jul, 2024 - 14:24:05 - Unknown alert received from device iictyiaplv077.msnet.railb.be of type Pingable. Device Time 0+00:00:00. (Trap type *******.*******.867.6.1) (event [0x00010801])", "ci_id": "iictyiaplv077", "node": "*************", "metric_name": "00010801", "clear_time": null, "event_id": "9850262", "node_alias": "*************", "actionable": false, "additional_data": "{\"cause_code\": \"10801\"}", "raise_time": "2024-07-04 12:24:05", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/04/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-04T12:24:08.241599430Z", "repair_person": "", "severity": "MINOR", "mhandle": "0x87e6492", "mname": "RsyslogServer", "alarm_id": "9850265", "event_message": "Thu 04 Jul, 2024 - 14:24:05 - Unknown alert received from device RsyslogServer of type Host_systemEDGE. Device Time 0+00:00:00. (Trap type *******.*******.867.6.1) (event [0x00010801])\nTrap var bind data: \nOID:  *******.*******.0  Value:  Thu Jul 04 12:24:05 UTC 2024\nOID:  *******.*******.4.1.0  Value:  *******.*******.867.1\nOID:  *******.********.3.0  Value:  *************\nOID:  *******.*******.867.2  Value:  36e39f4e-2e13-4b5a-abfe-698a67ecd4b8\nOID:  *******.*******.867.3  Value:  SYSTEM-MANAGED-SERVICES\nOID:  *******.*******.867.4  Value:  OK\nOID:  *******.*******.867.5  Value:   Managed Service elasticsearch is RUNNING \nOID:  *******.*******.867.6  Value:  []\nOID:  *******.*******.867.7  Value:  1.0.0\nOID:  *******.*******.867.8  Value:  Thu Jul 04 12:23:35 UTC 2024\nOID:  *******.*******.867.9  Value:  Cisco DNA Center System\nOID:  *******.*******.867.10  Value:  Platform Services\nOID:  *******.*******.867.11  Value:  INFO\nOID:  *******.*******.867.12  Value:  SYSTEM\nOID:  *******.*******.867.13  Value:  3\nOID:  *******.*******.867.14  Value:  DNAC API\nOID:  *******.*******.867.15  Value:  {\"Message\":\" Managed Service elasticsearch is RUNNING \",\"Instance\":\"dnacenter.msnet.railb.be : \",\"State\":\"OK\",\"Tags\":\"Managed Services\"}\n", "mtype": "Host_systemEDGE", "time": "14:24:05", "cause_code": "10801", "clearable": "TRUE", "event_type": "1", "dtype": "systemEDGE Host", "ipaddress": "*************", "mthandle": "0x1160088", "ackd": "FALSE", "secstr": "", "event.uuid": "3541800d-c834-4a15-ac19-7624abe421fb", "event.kafka.offset": 2100922, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-04T12:24:08.184Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Thu 04 Jul, 2024 - 14:24:05 - Unknown alert received from device RsyslogServer of type Host_systemEDGE. Device Time 0+00:00:00. (Trap type *******.*******.867.6.1) (event [0x00010801])", "ci_id": "RsyslogServer", "node": "*************", "metric_name": "00010801", "clear_time": null, "event_id": "9850265", "node_alias": "*************", "actionable": false, "additional_data": "{\"cause_code\": \"10801\"}", "raise_time": "2024-07-04 12:24:05", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/04/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-04T12:25:16.025158150Z", "repair_person": "", "severity": "MAJOR", "mhandle": "0x876241b", "mname": "wlc.msnet.railb.be", "alarm_id": "9843611", "event_message": "Thu 04 Jul, 2024 - 14:25:01 - An LP Associated event has occurred, from Rtr_Cisco device, named wlc.msnet.railb.be\n\ntrap = *******.*******.513.0.4\nLPname = lpschar53\n\n(event [0xfff0039e])\nOnly displaying most recent of 2 event messages.\n", "mtype": "Rtr_Cisco", "time": "14:25:01", "cause_code": "fff0032f", "clearable": "TRUE", "event_type": "0", "dtype": "Cisco 8500 WLC", "ipaddress": "***********", "mthandle": "0x21000c", "ackd": "FALSE", "secstr": "", "event.uuid": "7a50cf7d-4bdc-4e75-a9de-54e2c533ab5f", "event.kafka.offset": 2100923, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-04T12:25:15.989Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015"}, "output": {"severity": 3, "event_type": "clear", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "The WLC no longer is able to communicate with the access point", "ci_id": "lpschar53", "node": "***********", "metric_name": "fff0032f", "clear_time": "2024-01-01 12:00:01", "event_id": "9843611", "node_alias": "***********", "actionable": false, "additional_data": "{\"cause_code\": \"fff0032f\"}", "raise_time": "2024-07-04 12:25:01", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/04/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-04T12:25:14.597897609Z", "repair_person": "", "severity": "MAJOR", "mhandle": "0x876241b", "mname": "wlc.msnet.railb.be", "alarm_id": "9843611", "event_message": "Thu 04 Jul, 2024 - 14:25:01 - An LP Associated event has occurred, from Rtr_Cisco device, named wlc.msnet.railb.be\n\ntrap = *******.*******.513.0.4\nLPname = lpschar53\n\n(event [0xfff0039e])\nOnly displaying most recent of 2 event messages.\n", "mtype": "Rtr_Cisco", "time": "14:25:14", "cause_code": "fff0032f", "clearable": "TRUE", "event_type": "2", "dtype": "Cisco 8500 WLC", "ipaddress": "***********", "mthandle": "0x21000c", "ackd": "FALSE", "secstr": "", "event.uuid": "3cfba1df-ffbb-4185-b243-a33916ec5c07", "event.kafka.offset": 2105618, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-04T12:25:14.451Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016"}, "output": {"severity": 3, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "The WLC no longer is able to communicate with the access point", "ci_id": "lpschar53", "node": "***********", "metric_name": "fff0032f", "clear_time": null, "event_id": "9843611", "node_alias": "***********", "actionable": false, "additional_data": "{\"cause_code\": \"fff0032f\"}", "raise_time": "2024-07-04 12:25:14", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/04/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-04T12:33:46.708610277Z", "repair_person": "", "severity": "CRITICAL", "mhandle": "0x870000b", "mname": "Fault Isolation", "alarm_id": "9854694", "event_message": "Thu 04 Jul, 2024 - 14:28:45 - Fault Isolation has detected an unresolved fault (fault record id 1962).  The following is a list of models to which SPECTRUM has lost contact:\n\n    lpschar56 of type IP Device with IP Address ************* with a device criticality of 1\n\nAs a result of this condition, an alarm has been generated on this model.   (event [0x00010d05])\n", "mtype": "FaultIsolation", "time": "14:28:45", "cause_code": "10703", "clearable": "FALSE", "event_type": "1", "dtype": "N/A", "ipaddress": " ", "mthandle": "0x10413", "ackd": "FALSE", "secstr": "ADMIN", "event.uuid": "eb37768b-1859-4b97-9db8-5d4d6d568933", "event.kafka.offset": 2100924, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-04T12:33:46.674Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Thu 04 Jul, 2024 - 14:28:45 - Fault Isolation has detected an unresolved fault (fault record id 1962).  The following is a list of models to which SPECTRUM has lost contact:", "ci_id": "Fault Isolation", "node": " ", "metric_name": "00010703", "clear_time": null, "event_id": "9854694", "node_alias": " ", "actionable": false, "additional_data": "{\"cause_code\": \"10703\"}", "raise_time": "2024-07-04 12:28:45", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/04/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-04T12:33:48.165210192Z", "repair_person": "", "severity": "CRITICAL", "mhandle": "0x870000b", "mname": "Fault Isolation", "alarm_id": "9854694", "event_message": "Thu 04 Jul, 2024 - 14:33:45 - Fault Isolation alarm was cleared (fault record id 1962). (event [0x00010d32])\nOnly displaying most recent of 2 event messages.\n", "mtype": "FaultIsolation", "time": "14:33:47", "cause_code": "10703", "clearable": "FALSE", "event_type": "2", "dtype": "N/A", "ipaddress": " ", "mthandle": "0x10413", "ackd": "FALSE", "secstr": "ADMIN", "event.uuid": "2a1e33b6-6565-4061-ad8c-e53fbdfce583", "event.kafka.offset": 2102766, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-04T12:33:48.127Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Thu 04 Jul, 2024 - 14:33:45 - Fault Isolation alarm was cleared (fault record id 1962). (event [0x00010d32])", "ci_id": "Fault Isolation", "node": " ", "metric_name": "00010703", "clear_time": null, "event_id": "9854694", "node_alias": " ", "actionable": false, "additional_data": "{\"cause_code\": \"10703\"}", "raise_time": "2024-07-04 12:33:47", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/05/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-04T22:01:28.128130567Z", "repair_person": "", "severity": "MINOR", "mhandle": "0x87763e0", "mname": "hssilly04.network.railb.be", "alarm_id": "10309330", "event_message": "Fri 05 Jul, 2024 - 00:01:26 - User operations@iictziaplv077 cleared alarm number 10309330 with probable cause id 0xfff0017a for device hssilly04.network.railb.be of type Pingable. (event [0x00010706])\nOnly displaying most recent of 2 event messages.\n", "mtype": "Pingable", "time": "00:01:27", "cause_code": "fff0017a", "clearable": "TRUE", "event_type": "2", "dtype": "IP Device", "ipaddress": "***********", "mthandle": "0x10290", "ackd": "FALSE", "secstr": "", "event.uuid": "349590ce-0754-4a8e-beca-0f3a7a9c3c57", "event.kafka.offset": 2106157, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-04T22:01:27.992Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016"}, "output": {"severity": 4, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "More then 20communication link up/downs on device", "ci_id": "hssilly04", "node": "***********", "metric_name": "fff0017a", "clear_time": null, "event_id": "10309330", "node_alias": "***********", "actionable": false, "additional_data": "{\"cause_code\": \"fff0017a\"}", "raise_time": "2024-07-04 22:01:27", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/06/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-05T22:00:50.714193163Z", "repair_person": "", "severity": "MAJOR", "mhandle": "0x87b7e57", "mname": "APIC Cluster", "alarm_id": "11582370", "event_message": "Sat 06 Jul, 2024 - 00:00:49 - Sat 06 Jul, 2024 - 00:00:49 - VNA FAULT event\n \n VNA FAULT received with the following details:\n ----------------------------------------------------------------\n F0132: Operational issues detected for VMM controller: iictmivclv001.msnet.railb.be with name iictmivclv001 in datacenter  Dc_Monceau in domain: ACI_VDS_Wonka due to error: Event channel from external VMM controller is down.\n\n Source: comp/prov-VMware/ctrlr-[ACI_VDS_Wonka]-iictmivclv001/fault-F0132\n entityId: 4294980046\n notificationId: comp/prov-VMware/ctrlr-[ACI_VDS_Wonka]-iictmivclv001/fault-F0132\n cause: operational-issues\n name: F0132\n rule: comp-ctrlr-operational-issues\n description: Operational issues detected for VMM controller: iictmivclv001.msnet.railb.be with name iictmivclv001 in datacenter  Dc_Monceau in domain: ACI_VDS_Wonka due to error: Event channel from external VMM controller is down.\n type: operational\n severity: UNKNOWN\n state: MODIFIED\n origSeverity: major\n occur: 1\n lc: soaking\n domain: external\n ack: false\n delegated: yes\n prevSeverity: major\n creation time: Fri 05 Jul,2024 - 23:56:00\n last update time: Sat 06 Jul,2024 - 00:00:49\n user domain ID: 4294967300\n dataSourceContext: ACI_4cf37a3d-233f-42a2-b4cf-330f6edd005f:**************:0:alarm\n unknown attr:  ; subject:-controller ; childAction:- ; lastTransition:-2024-07-05T23:56:37.921+02:00 ; highestSeverity:-major\n ================================================================\n Spectrum Event ID: 0x0673000d\n\nOnly displaying most recent of 3 event messages.\n", "mtype": "SDN_Cluster", "time": "00:00:50", "cause_code": "67300c0", "clearable": "TRUE", "event_type": "2", "dtype": "N/A", "ipaddress": "", "mthandle": "0x6730019", "ackd": "FALSE", "secstr": "", "event.uuid": "7c0663ff-9781-43f7-b529-0302bfb38da0", "event.kafka.offset": 2107603, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-05T22:00:50.574Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Sat 06 Jul, 2024 - 00:00:49 - Sat 06 Jul, 2024 - 00:00:49 - VNA FAULT event", "ci_id": "APIC Cluster", "node": "", "metric_name": "067300c0", "clear_time": null, "event_id": "11582370", "node_alias": "", "actionable": false, "additional_data": "{\"cause_code\": \"67300c0\"}", "raise_time": "2024-07-05 22:00:50", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/07/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-06T22:04:26.360837486Z", "repair_person": "", "severity": "CRITICAL", "mhandle": "0x870000b", "mname": "Fault Isolation", "alarm_id": "12683996", "event_message": "Sun 07 Jul, 2024 - 00:04:11 - Fault Isolation alarm was cleared (fault record id 2834). (event [0x00010d32])\nOnly displaying most recent of 2 event messages.\n", "mtype": "FaultIsolation", "time": "00:04:25", "cause_code": "10703", "clearable": "FALSE", "event_type": "2", "dtype": "N/A", "ipaddress": " ", "mthandle": "0x10413", "ackd": "FALSE", "secstr": "ADMIN", "event.uuid": "e17a074b-cf84-47e6-ba2d-e19c3c49e3b0", "event.kafka.offset": 2104176, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-06T22:04:26.326Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Sun 07 Jul, 2024 - 00:04:11 - Fault Isolation alarm was cleared (fault record id 2834). (event [0x00010d32])", "ci_id": "Fault Isolation", "node": " ", "metric_name": "00010703", "clear_time": null, "event_id": "12683996", "node_alias": " ", "actionable": false, "additional_data": "{\"cause_code\": \"10703\"}", "raise_time": "2024-07-06 22:04:25", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/08/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-07T22:00:37.072408515Z", "repair_person": "", "severity": "MAJOR", "mhandle": "0x87cae72", "mname": "APIC Cluster", "alarm_id": "13821437", "event_message": "Mon 08 Jul, 2024 - 00:00:34 - Mon 08 Jul, 2024 - 00:00:34 - VNA FAULT event\n \n VNA FAULT received with the following details:\n ----------------------------------------------------------------\n F1551: 20% of packets were received in excess during the last collection interval on the path from node 200 to node 201\n\n Source: dbgs/ac/path-200-to-201/trnst-102/fault-F1551\n entityId: 4294967301\n notificationId: dbgs/ac/path-200-to-201/trnst-102/fault-F1551\n cause: packets-dropped\n name: F1551\n rule: dbg-ac-trail-on-going-atomic-counter-trail-excess-major\n description: 20% of packets were received in excess during the last collection interval on the path from node 200 to node 201\n type: communications\n severity: UNKNOWN\n state: MODIFIED\n origSeverity: major\n occur: 1\n lc: soaking\n domain: infra\n ack: false\n delegated: no\n prevSeverity: major\n creation time: Tue 02 Jul,2024 - 21:24:00\n last update time: Mon 08 Jul,2024 - 00:00:34\n user domain ID: 4294967297\n dataSourceContext: ACI_69718bda-32d7-43c4-9c59-08d398af5fd6:*********:0:alarm\n unknown attr:  ; subject:-atomic-counter ; childAction:- ; lastTransition:-2024-07-02T21:24:21.239+02:00 ; highestSeverity:-major\n ================================================================\n Spectrum Event ID: 0x0673000d\n\nOnly displaying most recent of 2 event messages.\n", "mtype": "ACI_POD", "time": "00:00:36", "cause_code": "67300e9", "clearable": "TRUE", "event_type": "2", "dtype": "N/A", "ipaddress": "", "mthandle": "0x6730018", "ackd": "FALSE", "secstr": "", "event.uuid": "f14a6231-786b-4d7f-bf4d-9789ed9b0a36", "event.kafka.offset": 2107055, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-07T22:00:37.036Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Mon 08 Jul, 2024 - 00:00:34 - Mon 08 Jul, 2024 - 00:00:34 - VNA FAULT event", "ci_id": "APIC Cluster", "node": "", "metric_name": "067300e9", "clear_time": null, "event_id": "13821437", "node_alias": "", "actionable": false, "additional_data": "{\"cause_code\": \"67300e9\"}", "raise_time": "2024-07-07 22:00:36", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/09/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-08T22:01:25.167609349Z", "repair_person": "", "severity": "MAJOR", "mhandle": "0x87cae72", "mname": "APIC Cluster", "alarm_id": "15209247", "event_message": "Tue 09 Jul, 2024 - 00:01:23 - Tue 09 Jul, 2024 - 00:01:23 - CISCO ACI FAULT event\n \n CISCO ACI FAULT received with the following details:\n ----------------------------------------------------------------\n F1551: 20% of packets were received in excess during the last collection interval on the path from node 200 to node 201\n\n Source: dbgs/ac/path-200-to-201/trnst-102/fault-F1551\n entityId: 4294967301\n notificationId: dbgs/ac/path-200-to-201/trnst-102/fault-F1551\n cause: packets-dropped\n name: F1551\n rule: dbg-ac-trail-on-going-atomic-counter-trail-excess-major\n description: 20% of packets were received in excess during the last collection interval on the path from node 200 to node 201\n type: communications\n severity: MAJOR\n state: MODIFIED\n origSeverity: major\n occur: 1\n lc: soaking\n domain: infra\n ack: false\n delegated: no\n prevSeverity: major\n creation time: Tue 02 Jul,2024 - 21:24:00\n last update time: Tue 09 Jul,2024 - 00:01:23\n unknown attr:  ; subject:-atomic-counter ; childAction:- ; lastTransition:-2024-07-02T21:24:21.239+02:00 ; highestSeverity:-major\n ================================================================\n Spectrum Event ID: 0x067300e9\n\n", "mtype": "ACI_POD", "time": "00:01:23", "cause_code": "67300e9", "clearable": "TRUE", "event_type": "1", "dtype": "N/A", "ipaddress": "", "mthandle": "0x6730018", "ackd": "FALSE", "secstr": "", "event.uuid": "d09cd229-cbfa-423b-8539-14aa4c9d6561", "event.kafka.offset": 2111442, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-08T22:01:25.034Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Tue 09 Jul, 2024 - 00:01:23 - Tue 09 Jul, 2024 - 00:01:23 - CISCO ACI FAULT event", "ci_id": "APIC Cluster", "node": "", "metric_name": "067300e9", "clear_time": null, "event_id": "15209247", "node_alias": "", "actionable": false, "additional_data": "{\"cause_code\": \"67300e9\"}", "raise_time": "2024-07-08 22:01:23", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv077", "date": "07/10/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-09T22:00:15.559518132Z", "repair_person": "", "severity": "CRITICAL", "mhandle": "0x875b776", "mname": "lpooste49", "alarm_id": "16532007", "event_message": "Wed 10 Jul, 2024 - 00:00:14 The condition causing the loss of contact on the device model has cleared ( name - lpooste49, type - AccessPoint ). (event [0x00010d30])\nOnly displaying most recent of 2 event messages.\n", "mtype": "AccessPoint", "time": "00:00:15", "cause_code": "10009", "clearable": "FALSE", "event_type": "2", "dtype": "IP Device", "ipaddress": "*************", "mthandle": "0x6760005", "ackd": "FALSE", "secstr": "", "event.uuid": "dc2381d7-fb05-41d1-a41c-d8a5586c9d44", "event.kafka.offset": 2108385, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-09T22:00:15.525Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015"}, "output": {"severity": 5, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "DEVICE HAS STOPPED RESPONDING TO POLLS", "ci_id": "lpooste49", "node": "*************", "metric_name": "00010009", "clear_time": null, "event_id": "16532007", "node_alias": "*************", "actionable": false, "additional_data": "{\"cause_code\": \"10009\"}", "raise_time": "2024-07-09 22:00:15", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv097", "date": "07/10/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-10T21:51:07.844505958Z", "repair_person": "", "severity": "CRITICAL", "mhandle": "0x87d60c1", "mname": "lpcharl10", "alarm_id": "16860001", "event_message": "Wed 10 Jul, 2024 - 03:37:35 - <PERSON>ce lpcharl10 of type AccessPoint has stopped responding to polls and/or external requests.  An alarm will be generated.\n\n(event [0xfff00001])\n", "mtype": "AccessPoint", "time": "03:37:35", "cause_code": "10009", "clearable": "FALSE", "event_type": "1", "dtype": "IP Device", "ipaddress": "*************", "mthandle": "0x6760005", "ackd": "FALSE", "secstr": "", "event.uuid": "8bd2da1d-43ea-48a2-bd17-73f74dea3db8", "event.kafka.offset": 2111806, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-10T21:51:07.801Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016"}, "output": {"severity": 5, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "DEVICE HAS STOPPED RESPONDING TO POLLS", "ci_id": "lpcharl10", "node": "*************", "metric_name": "00010009", "clear_time": null, "event_id": "16860001", "node_alias": "*************", "actionable": false, "additional_data": "{\"cause_code\": \"10009\"}", "raise_time": "2024-07-10 01:37:35", "wake_up_time": "2024-01-01 12:02:01"}}, {"input": {"server": "iictyiaplv097", "date": "07/11/2024", "status": "", "alarm_state": "NEW", "@version": "1", "landscape": "0x8700000", "@timestamp": "2024-07-10T22:02:00.027093472Z", "repair_person": "", "severity": "MINOR", "mhandle": "0x872fd51", "mname": "rmpeile1v-m.network.railb.be", "alarm_id": "1238999", "event_message": "Wed 10 Jul, 2024 - 23:58:21 -  RMON falling threshold trap received from model rmpeile1v-m.network.railb.be of type JuniperJUNOSRtr.  AlarmIndex 1, AlarmVariable *******.********.1.1.10.646, AlarmSampleType 2, AlarmValue 902387653 and AlarmFallingThreshold 1000000000. (event [0x00010811])\nOnly displaying most recent of 2 event messages.\n", "mtype": "JuniperJUNOSRtr", "time": "00:01:59", "cause_code": "10810", "clearable": "TRUE", "event_type": "2", "dtype": "MX960", "ipaddress": "************", "mthandle": "0x3b10002", "ackd": "FALSE", "secstr": "", "event.uuid": "8918357b-0333-44b8-ad03-b74dd8cccedc", "event.kafka.offset": 2110232, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.timestamp": "2024-07-10T22:01:59.883Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015"}, "output": {"severity": 1, "event_type": "problem", "metric_type": "/HardwareEvent/", "agent_id": 0, "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "handle_time": "2024-01-01 12:00:01", "summary": "Wed 10 Jul, 2024 - 23:58:21 -  RMON falling threshold trap received from model rmpeile1v-m.network.railb.be of type JuniperJUNOSRtr.  AlarmIndex 1, AlarmVariable *******.********.1.1.10.646, AlarmSampleType 2, AlarmValue 902387653 and AlarmFallingThreshold 1000000000. (event [0x00010811])", "ci_id": "rmpeile1", "node": "************", "metric_name": "00010810", "clear_time": null, "event_id": "1238999", "node_alias": "************", "actionable": false, "additional_data": "{\"cause_code\": \"10810\"}", "raise_time": "2024-07-10 22:01:59", "wake_up_time": "2024-01-01 12:02:01"}}]}