import importlib
import json
import logging
from dataclasses import dataclass
from pathlib import Path
from unittest.mock import Magic<PERSON>ock

import pytest
from freezegun import freeze_time
from pytest_mock import Mocker<PERSON>ix<PERSON>

from olympus_common import core, db
from olympus_common.datareaders import DataReader
from olympus_common.datawriters import DataWriter
from olympus_common.logger import Logger
from tests.olympus_common.mocked_db import MockedDBModule
from tests.utils import dummy_environ, filter_outputs

FROZEN_TIME = "2024-01-01 12:00:01"


@dataclass
class MockedReader(DataReader):
    def read_data(self) -> list[dict]:
        raise Exception(
            "This method should be mocked.\n"
            "Example:\n"
            "mocked_read_data = mocker.patch.object(app.datareader, 'read_data')\n"
            "mocked_read_data.return_value = 'dummy value'"
        )

    def success(self, data: list[dict]) -> None:
        print("success", self, data)

    def error(self, data: list[dict], exc: Exception) -> None:
        print("error", self, data, exc)


@dataclass
class MockedWriter(DataWriter):
    def success(self, data: list[dict]) -> None:
        print("success", self, data)

    def error(self, data: list[dict], exc: Exception) -> None:
        print("error", self, data, exc)


def init_test_application() -> core.Application:
    datareader = MockedReader()
    datawriter = MockedWriter()
    logger = Logger(filename=None, level=logging.DEBUG)  # Log to console
    return core.Application(datareader=datareader, datawriter=datawriter, logger=logger)


@pytest.mark.parametrize(
    ("input_", "expected_call_count", "expected_success"),
    [
        ([], 0, False),  # No data, expect no calls and failure
        ([{"a": 0}], 1, True),  # Data present, expect one call and success
    ],
)
def test_run_parametrized(input_: list, expected_call_count: int, expected_success: bool, mocker: MockerFixture):
    """Assert that the function's success equals `expected_success` and call_count equals `expected_call_count`."""
    # Use a magicmock instance to be able to assert the call_count on the fn.
    fn = mocker.MagicMock()
    fn.__name__ = "dummy_data"  # Required for a print-statement in `_run`
    fn.side_effect = lambda data: data  # Return the provided `data` when calling this function

    # Initialize an Application instance configured for tests.
    app = init_test_application()

    # Mock app.datareader.read_data so that it returns the provided `input_`.
    mocked_read_data = mocker.patch.object(app.datareader, "read_data")
    mocked_read_data.return_value = input_

    # Run app._run
    assert app._run(fn) is expected_success
    assert fn.call_count == expected_call_count


def test_run_error(mocker: MockerFixture):
    """Assert that the function is never called when no data is provided."""
    # Use a magicmock instance to be able to assert the call_count on the fn.
    fn = mocker.MagicMock()
    fn.__name__ = "dummy_data_exception"  # Required for a print-statement in `_run`
    fn.side_effect = Exception  # Raise an exception when calling this function

    # Initialize an Application instance configured for tests.
    app = init_test_application()

    # Mock app.datareader.read_data so that we can control the return_value.
    mocked_read_data = mocker.patch.object(app.datareader, "read_data")
    mocked_read_data.return_value = [{"a": 0}]

    # Run app._run and assert that it failed and that the provided fn was called once
    with pytest.raises(RuntimeError, match="Error while running function dummy_data_exception"):
        app._run(fn)
    assert fn.call_count == 1


def _default_mock_args():
    return [{"target": "dd.db", "new": MockedDBModule()}]


def local6_mock_args(agent_name):
    env = dummy_environ()
    env["AGENT_NAME"] = agent_name
    config_mock = MagicMock()
    config_mock.agent_name = agent_name
    return [
        {"dict": True, "target": "os.environ", "value": env, "clear": True},
        {"target": "dd.config", "new": config_mock},
    ] + _default_mock_args()


def _default_occurrence(which: str = "last_heartbeat"):
    # TODO: move this as dynamic data to the input file.
    if which == "last_heartbeat":
        kwargs: dict = {
            "summary": "FSP-NM Heart Beat",
            "severity": 1,
            "event_type": "heartbeat",
            "raise_time": FROZEN_TIME,
            "clear_time": None,
            "additional_data": {"network_element_type": "fspNm"},
            "handle_time": FROZEN_TIME,
            "ci_id": "ADVA_HB",
            "agent_id": 1,
            "metric_type": "/ApplicationEvent/",
            "metric_name": "Heartbeat",
        }
    else:
        kwargs = {}
    return db.Occurrence(**kwargs)


@pytest.mark.parametrize(
    ("name", "convert_dates", "process_single", "mock_args", "suffix"),
    [
        ("mon_adva", True, True, _default_mock_args(), None),
        ("mon_ca_spectrum", True, True, _default_mock_args(), None),
        ("mon_cnms", True, True, _default_mock_args(), None),
        (
            "mon_datalines",
            True,
            True,
            _default_mock_args() + [{"target": "utils.socket.gethostbyaddr", "return_value": ["dummyhost.test"]}],
            None,
        ),
        # ("mon_dica", True, True, _default_mock_args()),  # No data
        ("mon_gsx", True, True, _default_mock_args(), None),
        ("mon_local6", True, True, local6_mock_args("local6"), None),
        ("mon_local6", True, True, local6_mock_args("HA"), "ha"),
        ("mon_lucent_oms", True, True, _default_mock_args(), None),
        ("mon_occ", True, True, _default_mock_args(), None),
        ("mon_openshift", True, False, _default_mock_args(), None),
        ("mon_pem", True, False, _default_mock_args(), None),
        ("mon_sap_solman", True, True, _default_mock_args(), None),
        ("mon_scom", True, True, _default_mock_args(), None),
        (
            "mon_stonebranch",
            True,
            False,
            _default_mock_args(),
            None,
        ),  # No process single as input/output is not in same order.
        ("mon_struxureware", True, False, _default_mock_args(), None),
        ("mon_vrealize", True, True, _default_mock_args(), None),
        ("mon_scada", True, True, _default_mock_args(), None),
        ("mon_websphere_mq", True, False, _default_mock_args(), None),
        # (
        #     "heartbeat_job",
        #     True,
        #     True,
        #     [
        #         {"target": "main.get_last_occurrence", "return_value": _default_occurrence("last")},
        #         {"target": "main.get_last_heartbeat", "return_value": _default_occurrence("last_heartbeat")},
        #     ],
        # ),  # Disabled due to the _default_occurrence not being easily mocked.
    ],
)
def test_service_runner(
    name: str, convert_dates: bool, process_single: bool, mock_args: list[dict], suffix: str, mocker: MockerFixture
):
    """Test the specific service runner.

    The services tested here have KafkaReaders with DatabaseWriters, the entire DB module is always mocked.

    The inputfile should be a json dict with keys "inputs" and "outputs", those should be lists of dicts.
    When an input record is supposed to be dropped from the output, it's required to have an empty object in the
    output-list.

    mock_args is used to mock certain functions in the service's main module. The target should be the function's
    full path without the name, e.g. to mock "mon_datalines.utils.ci_id" mock_args would be {"target": "utils.ci_id"}.
    The mock_args dictionary is used as **kwargs to `mocker.patch`.
    """
    for mock_arg in mock_args:
        if not mock_arg.get("dict"):
            mock_arg["target"] = f"{name}.{mock_arg['target']}"
            mocker.patch(**mock_arg)
        else:
            mocker.patch.dict(mock_arg["target"], mock_arg["value"], clear=mock_arg.get("clear", False))
    main_module = importlib.import_module(f"{name}.main")
    runner = main_module.main

    filename = name if not suffix else f"{name}_{suffix}"
    path = Path(__file__).parent / "data" / f"{filename}.json"
    testcontent = json.loads(path.read_text())
    testdata: list[dict[str, dict]] = testcontent["data"]
    filter_outputs(testdata, convert_dates=convert_dates)

    output = _get_output(runner, testdata)

    try:
        compare_outputs(output, testdata, ignore_key="ignore_in_batch")
    except (ValueError, AssertionError) as exc:
        raise AssertionError(f"Failed for {name=}\n{exc}") from exc

    if not process_single:
        return

    for idx, item in enumerate(testdata):
        items = [item]
        output = _get_output(runner, items)
        try:
            compare_outputs(output, items)
        except (ValueError, AssertionError) as exc:
            raise AssertionError(f"Failed for {name=}, {idx=}, {output=}, {item=}\n{exc}") from exc


def compare_outputs(output: list[dict], data: list[dict], ignore_key: str | None = None) -> None:
    """Compare outputs by ensuring that `expected_output`'s records are the same as those of `output`.

    Parameters
    ----------
    output : list[dict]
        The output that can contain other keys.
    data : list[dict]
        The data containing the the output used to verify the data in `output`

    Raises
    ------
    AssertionError
        In case a key present in `output_expected` was not found in `output`.
        In case the value for a key in `output_expected` does not match in `output`.

    Notes
    -----
    This function attempts to zip the output with the expected outputs from `data`, this is converted to a list
    to ensure that both are of the same length before processing other data.
    """
    expected_outputs = [outp for item in data if ignore_key not in item and (outp := item["output"])]
    outputs = list(zip(output, expected_outputs, strict=True))

    for idx, (record, expected) in enumerate(outputs):
        for key in expected.keys():
            if key not in record:
                raise AssertionError(f"{idx=}: {key=} was not found in {record=}")
            assert record[key] == expected[key], (
                f"{idx=}: {key=} does not match. {record[key]=} != {expected[key]=}. {record=}"
            )


def _get_output(runner, data):
    """Run the `runner`'s wrapped function and pass it the `input` from the `data`.

    Time is frozen at `FROZEN_TIME` so that we can choose what `datetime.now()` returns.
    """
    inputs = [item["input"] for item in data]
    with freeze_time(FROZEN_TIME):
        output = runner.__wrapped__(inputs)
    return output
