"""Utility functions for the tests."""

from tempfile import TemporaryDirectory

from dateutil import parser
from fastapi import Fast<PERSON><PERSON>
from starlette.middleware import Middleware

from olympus_common.db import Alarm, Occurrence

DATE_COLUMNS = ["raise_time", "clear_time", "wake_up_time", "handle_time"]


def dummy_environ() -> dict[str, str]:
    """Return a dict which can be used as os.environ in testing."""
    with TemporaryDirectory() as tmpdir:
        return dict(
            DEBUG="1",
            CHECKPOINTS_FOLDER=f"{tmpdir}/checkpoints",
            LOGS_FOLDER=f"{tmpdir}/logs",
            KAFKA_TOPICS='["dummy-topic"]',
            KAFKA_BOOTSTRAP_SERVERS='["dummy-server-001:9093", "dummy-server-002:9093", "dummy-server-003:9093"]',
            DB_HOST="dummyhost",
            DB_PORT="5432",
            DB_NAME="dummyname",
            DB_USER="dummyuser",
            DB_PASSWORD="dummypassword",  # noqa: S106
            KAFKA_USER="dummyuser",
            KAFKA_PASSWORD="dummypassword",  # noqa: S106
            KAFKA_SECURITY_PROTOCOL="dummysecurityprotocol",
            KAFKA_SASL_MECHANISM="dummysaslmechanism",
            KAFKA_ENVIRONMENT="dummyenv",
            KAFKA_CLIENT_ID_SUFFIX="mon-dummy",
            JWK_URI="https://claim.test.com",
            JWK_VALID_AUDIENCES='["dummy-audience"]',
            ELASTIC_APM_SERVER_URL="http://dummyapm",
            ENABLE_ELASTIC_APM="False",
            ELASTIC_APM_SECRET_TOKEN="dummytoken",  # noqa: S106,
            OLYMPUS_SERVICE_NAME="dummy-service",
        )


def filter_outputs(data: list[dict], convert_dates: bool = True, pop_invalid_keys: bool = True) -> None:
    """Filter all fields that are not in Alarm or Occurrence and convert DATE_COLUMNS to datetimes.

    Parameters
    ----------
    data: list[dict]
        the data of which the outputs should be filtered.
    convert_dates : bool, optional
        Whether or not dates should be converted, by default True
    pop_invalid_keys : bool, optional
        Whether or not keys that are no present on Alarm or Occurrence should be popped, by default True
    """
    for item in data:
        record: dict = item["output"]
        for key in list(record.keys()):
            if pop_invalid_keys and (key not in Alarm.columns() and key not in Occurrence.columns()):
                record.pop(key)
                continue
            if convert_dates and key in DATE_COLUMNS and record[key]:
                record[key] = parser.parse(record[key], ignoretz=True)


def remove_middleware(app: FastAPI, target: str) -> FastAPI:
    """Remove middleware from the app."""
    new_middlewares: list[Middleware] = []
    for middleware in app.user_middleware:
        if not middleware.cls.__name__ == target:
            new_middlewares.append(middleware)
    app.user_middleware = new_middlewares
    app.middleware_stack = app.build_middleware_stack()
    return app
